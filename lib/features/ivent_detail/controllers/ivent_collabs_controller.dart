import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class IventCollabsController extends BaseControllerWithSearch<IventDetailSharedState> {
  IventCollabsController(AuthService authService, IventDetailSharedState state) : super(authService, state);

  final _collabsResult = Rxn<SearchCollabsReturn>();

  SearchCollabsReturn? get collabsResult => _collabsResult.value;

  @override
  bool get isResultsEmpty => collabsResult?.collabs.isEmpty ?? true;

  @override
  InitialSearchResultsState get initialSearchResultsState => InitialSearchResultsState.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _collabsResult.value = await iventCollabsApi.searchCollabs(
      state.iventId,
      q: query,
    );
  }
}
