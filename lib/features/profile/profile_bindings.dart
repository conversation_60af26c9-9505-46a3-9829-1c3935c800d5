import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_content_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_created_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_favorites_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_followers_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_followings_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_friends_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_joined_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_social_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';

class ProfileBindings implements Bindings {
  final String userId;
  final bool permanent;

  ProfileBindings({required this.userId, this.permanent = false});

  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final sharedState = Get.put(ProfileSharedState(userId), tag: userId, permanent: permanent);

    Get.lazyPut<ProfileContentController>(() => ProfileContentController(service, sharedState),
        tag: userId, fenix: true);
    Get.lazyPut<ProfileCreatedIventsController>(() => ProfileCreatedIventsController(service, sharedState),
        tag: userId, fenix: true);
    Get.lazyPut<ProfileJoinedIventsController>(() => ProfileJoinedIventsController(service, sharedState),
        tag: userId, fenix: true);
    Get.lazyPut<ProfileIventsController>(() => ProfileIventsController(service, sharedState), tag: userId, fenix: true);
    Get.lazyPut<ProfileFavoritesController>(() => ProfileFavoritesController(service, sharedState),
        tag: userId, fenix: true);
    Get.lazyPut<ProfileFollowersController>(() => ProfileFollowersController(service, sharedState),
        tag: userId, fenix: true);
    Get.lazyPut<ProfileFriendsController>(() => ProfileFriendsController(service, sharedState),
        tag: userId, fenix: true);
    Get.lazyPut<ProfileFollowingsController>(() => ProfileFollowingsController(service, sharedState),
        tag: userId, fenix: true);
    Get.lazyPut<ProfileUserInfoController>(() => ProfileUserInfoController(service, sharedState),
        tag: userId, fenix: true);
    Get.lazyPut<ProfileSocialController>(() => ProfileSocialController(service, sharedState), tag: userId, fenix: true);
  }
}
