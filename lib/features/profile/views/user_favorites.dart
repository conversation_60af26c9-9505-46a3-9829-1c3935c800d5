import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/enums/account_enum.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_ivent_tile.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';
import 'package:ivent_app/features/profile/controllers/profile_favorites_controller.dart';

class UserFavorites extends GetView<ProfileFavoritesController> {
  final String userId;

  const UserFavorites({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Favoriler',
      textEditingController: controller.textController,
      body: Obx(() {
        final favoritesResult = controller.favoritesResult;
        return IaSearchPlaceholder(
          entityName: 'iVent',
          isSearching: controller.isSearching,
          isResultsEmpty: controller.isResultsEmpty,
          isQueryEmpty: controller.isQueryEmpty,
          initialSearchResultsState: InitialSearchResultsState.loaded,
          builder: (context) => ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: favoritesResult!.iventCount,
            itemBuilder: (context, index) {
              final ivent = favoritesResult.ivents[index];
              return _SearchResult(controller: controller, ivent: ivent);
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          ),
        );
      }),
    );
  }
}

class _SearchResult extends StatelessWidget {
  const _SearchResult({
    required this.controller,
    required this.ivent,
  });

  final ProfileFavoritesController controller;
  final IventListItemWithIsFavorited ivent;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isFavorited = controller.favoritedIventIds.contains(ivent.iventId);
      return IaIventTile(
        imageUrl: ivent.thumbnailUrl,
        iventName: ivent.iventName,
        locationName: ivent.locationName,
        date: ivent.dates.map((e) => DateTime.parse(e)).toList(),
        memberAvatarUrls: ivent.memberAvatarUrls,
        memberCount: ivent.memberCount,
        memberNames: ivent.memberFirstnames,
        isOrganizerUser: ivent.creatorType != AccountEnum.DISTRIBUTOR,
        organizerName: ivent.creatorUsername,
        organizerAvatarUrl: ivent.creatorImageUrl,
        viewType: ivent.viewType,
        onTap: () => Get.toNamed(IventDetailPages.iventDetail, arguments: ivent.iventId),
        isFavorited: isFavorited,
        onFavorite: () => controller.toggleFavorite(ivent.iventId),
      );
    });
  }
}
