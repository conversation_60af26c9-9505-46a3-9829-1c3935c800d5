import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class ProfileUserInfoController extends BaseController<ProfileSharedState> {
  final userPageInfo = Rxn<GetUserByUserIdReturn>();
  final isFollowing = false.obs;
  final relationshipStatus = Rxn<UserRelationshipStatusEnum>();

  ProfileUserInfoController(AuthService authService, ProfileSharedState state) : super(authService, state);

  @override
  Future<void> initController() async {
    super.initController();
    debugPrint('UserInfoController initialized for user: ${state.userId}');
    await _loadUserInfo();
  }

  Future<void> toggleFollowing() async {
    await runSafe(
      () async {
        if (isFollowing.value) {
          isFollowing.value = false;
          await usersApi.unfollowByUserId(state.userId);
        } else {
          isFollowing.value = true;
          await usersApi.followByUserId(state.userId);
        }
      },
      tag: 'toggleFollowing',
    );
  }

  Future<void> toggleFriendship() async {
    await runSafe(
      () async {
        if (relationshipStatus == UserRelationshipStatusEnum.accepted) {
          relationshipStatus.value = null;
          await userRelationshipsApi.removeFriendByUserId(state.userId);
        } else {
          relationshipStatus.value = UserRelationshipStatusEnum.accepted;
          await userRelationshipsApi.inviteFriendByUserId(state.userId);
        }
      },
      tag: 'toggleFriendship',
    );
  }

  Future<void> _loadUserInfo() async {
    await runSafe(tag: 'loadUserInfo', () async {
      userPageInfo.value = await usersApi.getByUserId(state.userId);
      isFollowing.value = userPageInfo.value!.isFollowing;
      relationshipStatus.value = userPageInfo.value!.relationshipStatus;
      state.userRole = userPageInfo.value!.userRole;
    });
  }
}
