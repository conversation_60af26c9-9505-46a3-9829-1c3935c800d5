import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class ProfileContentController extends BaseController<ProfileSharedState> {
  final vibeFoldersReturn = Rxn<GetVibeFoldersByUserIdReturn>();
  final memoryFoldersReturn = Rxn<GetMemoryFoldersByUserIdReturn>();

  ProfileContentController(AuthService authService, ProfileSharedState state) : super(authService, state);

  @override
  Future<void> initController() async {
    super.initController();
    await loadFolders();
  }

  Future<void> loadFolders() async {
    await runSafe(tag: 'loadFolders', () async {
      final results = await Future.wait([
        authService.usersApi.getVibeFoldersByUserId(state.userId),
        authService.usersApi.getMemoryFoldersByUserId(state.userId),
      ]);

      vibeFoldersReturn.value = results[0] as GetVibeFoldersByUserIdReturn?;
      memoryFoldersReturn.value = results[1] as GetMemoryFoldersByUserIdReturn?;
    });
  }
}
