import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class ProfileJoinedIventsController extends BaseControllerWithSearch<ProfileSharedState> {
  final _iventsResult = Rxn<GetIventsByUserIdReturn>();

  ProfileJoinedIventsController(AuthService authService, ProfileSharedState state) : super(authService, state);

  GetIventsByUserIdReturn? get iventsResult => _iventsResult.value;

  @override
  bool get isResultsEmpty => iventsResult?.ivents.isEmpty ?? true;

  @override
  InitialSearchResultsState get initialSearchResultsState => InitialSearchResultsState.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _iventsResult.value = await usersApi.getIventsByUserId(
      state.userId,
      IventListingTypeEnum.joined,
      q: query,
    );
  }
}
