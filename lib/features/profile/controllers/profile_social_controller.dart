import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class ProfileSocialController extends BaseController<ProfileSharedState> {
  ProfileSocialController(AuthService authService, ProfileSharedState state) : super(authService, state);

  final _hobbies = Rx<SearchHobbiesReturn?>(null);
  final _groups = RxList<GroupListItem>([]);
  final _groupDetail = Rx<GetGroupByGroupIdReturn?>(null);
  final _groupCreatedName = ''.obs;
  final _groupCreatedThumbnailUrl = ''.obs;
  final _groupCreatedUserIds = RxList<String>([]);

  SearchHobbiesReturn? get hobbies => _hobbies.value;
  List<GroupListItem> get groups => _groups;
  GetGroupByGroupIdReturn? get groupDetail => _groupDetail.value;
  String get groupCreatedName => _groupCreatedName.value;
  String get groupCreatedThumbnailUrl => _groupCreatedThumbnailUrl.value;
  List<String> get groupCreatedUserIds => _groupCreatedUserIds;

  set hobbies(SearchHobbiesReturn? value) => _hobbies.value = value;
  set groups(List<GroupListItem> value) => _groups.assignAll(value);
  set groupDetail(GetGroupByGroupIdReturn? value) => _groupDetail.value = value;
  set groupCreatedName(String value) => _groupCreatedName.value = value;
  set groupCreatedThumbnailUrl(String value) => _groupCreatedThumbnailUrl.value = value;
  set groupCreatedUserIds(List<String> value) => _groupCreatedUserIds.assignAll(value);
}
