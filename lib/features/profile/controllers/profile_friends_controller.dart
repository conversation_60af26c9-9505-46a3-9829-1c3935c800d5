import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class ProfileFriendsController extends BaseControllerWithSearch<ProfileSharedState> {
  final _friendsResult = Rxn<SearchFriendsByUserIdReturn>();

  ProfileFriendsController(AuthService authService, ProfileSharedState state) : super(authService, state);

  SearchFriendsByUserIdReturn? get friendsResult => _friendsResult.value;

  @override
  bool get isResultsEmpty => friendsResult?.friends.isEmpty ?? true;

  @override
  InitialSearchResultsState get initialSearchResultsState => InitialSearchResultsState.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _friendsResult.value = await userRelationshipsApi.searchFriendsByUserId(
      state.userId,
      FriendListingTypeEnum.user,
      q: query,
    );
  }
}
