import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class ProfileFollowersController extends BaseControllerWithSearch<ProfileSharedState> {
  final _followersResult = Rxn<GetFollowersByUserIdReturn>();

  ProfileFollowersController(AuthService authService, ProfileSharedState state) : super(authService, state);

  GetFollowersByUserIdReturn? get followersResult => _followersResult.value;

  @override
  bool get isResultsEmpty => followersResult?.followers.isEmpty ?? true;

  @override
  InitialSearchResultsState get initialSearchResultsState => InitialSearchResultsState.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _followersResult.value = await usersApi.getFollowersByUserId(
      state.userId,
      q: query,
    );
  }
}
