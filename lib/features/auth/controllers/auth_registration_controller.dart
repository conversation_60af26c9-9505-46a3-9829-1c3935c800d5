import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class AuthRegistrationController extends BaseControllerWithSearch<AuthSharedState> {
  AuthRegistrationController(AuthService authService, AuthSharedState state) : super(authService, state);

  @override
  bool get isResultsEmpty => false;

  @override
  InitialSearchResultsState get initialSearchResultsState => InitialSearchResultsState.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      searchResults.assignAll(allHobbies);
    } else {
      searchResults.assignAll(allHobbies
          .where((hobby) => hobby.hobbyName.contains(query) || (hobby.parentHobbyName?.contains(query) ?? false))
          .toList());
    }
  }

  // Name page
  final fullnameTextController = TextEditingController();
  final fullname = ''.obs;
  final canContinueToHobbiesPage = false.obs;

  bool get isFullnameValid =>
      fullname.trim().length >= AuthValidationConstants.fullNameMinLength &&
      fullname.trim().length <= AuthValidationConstants.fullNameMaxLength;

  void handleFullnameChanged(String value) => fullname.value = value;
  void handleFullnameValidationChanged(bool isValid) => canContinueToHobbiesPage.value = isValid;

  // Hobbies page
  final checkedHobbyIds = <String>[].obs;
  final searchResults = <Hobby>[].obs;
  final allHobbiesByCategories = {
    'Müzik': Hobby.hobbyListByParentHobbyName['Müzik']!,
    'Sanat & Kültür': Hobby.hobbyListByParentHobbyName['Sanat & Kültür']!,
    'Spor': Hobby.hobbyListByParentHobbyName['Spor']!,
    'Kariyer & Akademik': Hobby.hobbyListByParentHobbyName['Kariyer & Akademik']!,
    'Yeme İçme': Hobby.hobbyListByParentHobbyName['Yeme İçme']!,
    'Toplum': Hobby.hobbyListByParentHobbyName['Toplum']!,
  };
  final allHobbies = Hobby.hobbyList;

  bool get areHobbiesValid => checkedHobbyIds.length >= AuthValidationConstants.minRequiredHobbies;

  List<String> get hobbyCategories => allHobbiesByCategories.keys.toList();
  List<List<Hobby>> get hobbyLists => allHobbiesByCategories.values.toList();

  void toggleHobby(String hobbyId) {
    if (checkedHobbyIds.contains(hobbyId)) {
      checkedHobbyIds.remove(hobbyId);
    } else {
      checkedHobbyIds.add(hobbyId);
    }
  }

  Future<void> _registerUser() async {
    await runSafe(() async {
      final result = await usersApi.register(
        RegisterDto(
          fullname: fullname.value,
          phoneNumber: state.formattedPhoneNumber,
          hobbyIds: checkedHobbyIds,
        ),
      );

      if (result == null) {
        goToSomethingWentWrongPage();
        return;
      }

      await authService.login(
        SessionUser(
          token: result.token,
          sessionId: result.userId,
          sessionRole: result.role,
          sessionUsername: result.username,
          sessionFullname: result.fullname,
          sessionAvatarUrl: result.avatarUrl,
        ),
      );
    });
  }

  Future<void> completeRegistration() async {
    await _registerUser();
    Get.toNamed(AuthPages.accessPage);
  }
}
