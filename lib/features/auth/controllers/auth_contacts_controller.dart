import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:ivent_app/features/auth/models/contact.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class AuthContactsController extends BaseControllerWithSearch<AuthSharedState> {
  AuthContactsController(AuthService authService, AuthSharedState state) : super(authService, state);

  @override
  bool get isResultsEmpty => searchResults.isEmpty;

  @override
  InitialSearchResultsState get initialSearchResultsState => InitialSearchResultsState.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      searchResults.assignAll(contacts);
    } else {
      searchResults.assignAll(contacts
          .where((contact) => (contact.username?.contains(query) ?? false) || contact.phoneNumber.contains(query))
          .toList());
    }
  }

  @override
  Future<void> initController() async {
    super.initController();
    if (state.accessToContacts) await _loadContacts();
  }

  final pendingContactIds = <String>[].obs;
  final getContactsReturn = Rxn<GetContactsByUserIdReturn>();
  final contacts = <ContactItem>[].obs;
  final searchResults = <ContactItem>[].obs;

  int get pendingInvitationsCount => pendingContactIds.length;

  Future<void> goToContactsPage(bool accessGranted) async {
    await runSafe(() async {
      Get.toNamed(AuthPages.contactsPage, arguments: accessGranted);
    });
  }

  Future<void> _loadContacts() async {
    await runSafe(() async {
      final phoneNumbers = await _getDeviceContactPhoneNumbers();

      getContactsReturn.value = await usersApi.getContactsByUserId(
        sessionUser.sessionId,
        GetContactsByUserIdDto(phoneNumbers: phoneNumbers),
      );

      contacts.assignAll(getContactsReturn.value!.contacts.map((e) => ContactItem.fromUserListItem(e)).toList());
      contacts.assignAll(phoneNumbers.map((e) => ContactItem.fromPhoneNumber(e)).toList());
    });
  }

  Future<List<String>> _getDeviceContactPhoneNumbers() async {
    if (!await FlutterContacts.requestPermission()) return [];

    final contacts = await FlutterContacts.getContacts(
      withProperties: true,
      withPhoto: false,
    );

    return contacts.map((contact) => contact.phones.map((phone) => phone.number).toList()).expand((x) => x).toList();
  }

  Future<void> toggleFriendRequest(String userId) async {
    await runSafe(() async {
      if (pendingContactIds.contains(userId)) {
        await _cancelFriendRequest(userId);
      } else {
        await _sendFriendRequest(userId);
      }
    });
  }

  UserRelationshipStatusEnum? getRelationshipStatus(String userId) {
    if (!pendingContactIds.contains(userId)) return null;
    return UserRelationshipStatusEnum.pending;
  }

  Future<void> _sendFriendRequest(String userId) async {
    await userRelationshipsApi.inviteFriendByUserId(userId);
    pendingContactIds.add(userId);
  }

  Future<void> _cancelFriendRequest(String userId) async {
    await userRelationshipsApi.removeFriendByUserId(userId);
    pendingContactIds.remove(userId);
  }
}
