import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/home/<USER>/home_accounts_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_ivents_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_search_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

class SearchScreen extends GetView<HomeSearchController> {
  const SearchScreen({Key? key}) : super(key: key);

  HomeAccountsController get searchAccountsController => controller.searchAccountsController;
  HomeIventsController get searchIventsController => controller.searchIventsController;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) => Get.back(),
      child: Obx(() {
        return IaSearchScreen(
          margin: const EdgeInsets.all(AppDimensions.padding20),
          textController: controller.searchTabIndex.value == 0
              ? searchAccountsController.textController
              : searchIventsController.textController,
          body: _buildBody(),
        );
      }),
    );
  }

  Widget _buildBody() {
    return DefaultTabController(
      initialIndex: 0,
      length: 2,
      child: Column(
        children: [
          SizedBox(
            height: 40,
            child: TabBar(
              dividerHeight: 4,
              indicator: UnderlineTabIndicator(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                borderSide: const BorderSide(width: 4, color: AppColors.primary),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: AppColors.lightGrey,
              overlayColor: const WidgetStatePropertyAll(Colors.transparent),
              onTap: (index) => controller.searchTabIndex.value = index,
              labelStyle: AppTextStyles.size16Medium,
              unselectedLabelStyle: AppTextStyles.size16MediumTextSecondary,
              tabs: const [
                Tab(text: 'iVent\'ler'),
                Tab(text: 'Hesaplar'),
              ],
            ),
          ),
          const SizedBox(height: AppDimensions.padding16),
          Expanded(
            child: TabBarView(
              physics: const ScrollPhysics(),
              children: [
                _buildIventTab(),
                _buildAccountTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIventTab() {
    return Obx(() {
      final ivents = searchIventsController.searchedIventsResults;
      return IaSearchPlaceholder(
        entityName: 'iVent',
        isSearching: searchIventsController.isSearching,
        isResultsEmpty: searchIventsController.isResultsEmpty,
        isQueryEmpty: searchIventsController.isQueryEmpty,
        initialSearchResultsState: InitialSearchResultsState.mustSearch,
        builder: (context) {
          return Column(
            children: [
              IventGrid(
                iventItems: ivents,
                onLoadMore: () {},
                onRefresh: () {},
              ),
            ],
          );
        },
      );
    });
  }

  Widget _buildAccountTab() {
    return Obx(() {
      final accounts = searchAccountsController.searchedAccountResults;
      return IaSearchPlaceholder(
        entityName: 'Hesap',
        isSearching: searchAccountsController.isSearching,
        isResultsEmpty: searchAccountsController.isResultsEmpty,
        isQueryEmpty: searchAccountsController.isQueryEmpty,
        initialSearchResultsState: InitialSearchResultsState.mustSearch,
        builder: (context) {
          return ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: accounts.length,
            itemBuilder: (context, index) {
              final account = accounts[index];
              return IaListTile.withImageUrl(
                onTap: () => Get.toNamed(ProfilePages.userProfile, arguments: account.accountId),
                avatarUrl: account.accountImageUrl,
                title: account.accountName,
              );
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          );
        },
      );
    });
  }
}
