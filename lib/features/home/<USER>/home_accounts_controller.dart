import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class HomeAccountsController extends BaseControllerWithSearch<HomeSharedState> {
  HomeAccountsController(AuthService authService, HomeSharedState state) : super(authService, state);

  final searchedAccountResults = <BasicAccountListItem>[].obs;

  @override
  bool get isResultsEmpty => searchedAccountResults.isEmpty;

  @override
  InitialSearchResultsState get initialSearchResultsState => InitialSearchResultsState.mustSearch;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      searchedAccountResults.value = [];
      return;
    }
    final response = await homeApi.searchAccount(query);
    if (response != null) {
      searchedAccountResults.value = response.accounts;
    } else {
      searchedAccountResults.value = [];
    }
  }
}
