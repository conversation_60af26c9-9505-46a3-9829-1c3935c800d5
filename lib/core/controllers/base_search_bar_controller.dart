import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';

class BaseSearchBarController extends GetxController {
  final Function(String? textInput) onSearch;
  final InitialSearchResultsState initialSearchResultsState;
  final int debounceDuration;

  BaseSearchBarController(
    this.onSearch,
    this.initialSearchResultsState, {
    this.debounceDuration = 500,
  });

  Timer? _debounceTimer;

  final _textController = TextEditingController();
  final _isSearching = false.obs;

  TextEditingController get textController => _textController;
  String get text => _textController.text;
  bool get isSearching => _isSearching.value;

  set text(String value) => _textController.text = value;
  set isSearching(bool value) => _isSearching.value = value;

  @override
  void onInit() async {
    super.onInit();
    _setupSearchListener();
    _initialSearch();
  }

  @override
  void onClose() {
    _debounceTimer?.cancel();
    textController.dispose();
    super.onClose();
  }

  void _setupSearchListener() {
    textController.addListener(() {
      _debounceTimer?.cancel();
      _debounceTimer = Timer(Duration(milliseconds: debounceDuration), () async {
        final textInput = textController.text.trim();
        if (textInput.isNotEmpty) {
          await safeSearch(textInput);
        } else {
          clearSearch();
        }
      });
    });
  }

  Future<void> _initialSearch() async {
    if (initialSearchResultsState == InitialSearchResultsState.loaded) {
      await safeSearch(null);
    }
  }

  Future<void> clearSearch() async {
    textController.clear();
    await _initialSearch();
  }

  Future<void> safeSearch(String? query) async {
    if (isSearching == true) return;
    try {
      isSearching = true;
      await onSearch(query);
    } catch (e, st) {
      _handleError(e, st: st);
    } finally {
      isSearching = false;
    }
  }

  void _handleError(dynamic e, {StackTrace? st}) {
    Get.defaultDialog(
      title: 'Hata',
      middleText: e.toString(),
      backgroundColor: AppColors.primary,
      titleStyle: AppTextStyles.size20Bold.copyWith(color: AppColors.white),
      middleTextStyle: AppTextStyles.size16Regular.copyWith(color: AppColors.white),
      barrierDismissible: true,
      radius: AppDimensions.radiusS,
      confirm: ElevatedButton(
        style: ElevatedButton.styleFrom(backgroundColor: AppColors.white),
        onPressed: () => Get.back(), // close the dialog
        child: Text('Tamam', style: AppTextStyles.size16Bold.copyWith(color: AppColors.primary)),
      ),
    );
    debugPrint('Exception: $e');
  }
}
